package com.example.springvueapp.devops.mapper;

import com.example.springvueapp.devops.entity.DevOpsCiTaskInstanceEntity;
import com.example.springvueapp.devops.model.DevOpsCiTaskInstance;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * DevOps CI任务实例的实体与DTO转换器
 */
@Component
public class DevOpsCiTaskInstanceMapper {



    /**
     * 将实体转换为DTO
     * @param entity CI任务实例实体
     * @return CI任务实例DTO
     */
    public DevOpsCiTaskInstance toDto(DevOpsCiTaskInstanceEntity entity) {
        if (entity == null) {
            return null;
        }

        return DevOpsCiTaskInstance.builder()
                .id(entity.getId())
                .ciTaskId(entity.getCiTaskId())
                .instanceId(entity.getInstanceId())
                .errorMessage(entity.getErrorMessage())
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为实体
     * @param dto CI任务实例DTO
     * @return CI任务实例实体
     */
    public DevOpsCiTaskInstanceEntity toEntity(DevOpsCiTaskInstance dto) {
        if (dto == null) {
            return null;
        }

        return DevOpsCiTaskInstanceEntity.builder()
                .id(dto.getId())
                .ciTaskId(dto.getCiTaskId())
                .instanceId(dto.getInstanceId())
                .errorMessage(dto.getErrorMessage())
                .userId(dto.getUserId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为新建实体（不包含ID和时间戳）
     * @param dto CI任务实例DTO
     * @param ciTaskId CI任务ID
     * @param userId 用户ID
     * @return CI任务实例实体
     */
    public DevOpsCiTaskInstanceEntity toNewEntity(DevOpsCiTaskInstance dto, Long ciTaskId, Long userId) {
        if (dto == null) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now();
        return DevOpsCiTaskInstanceEntity.builder()
                .ciTaskId(ciTaskId)
                .instanceId(dto.getInstanceId())
                .errorMessage(dto.getErrorMessage())
                .userId(userId)
                .createdAt(now)
                .updatedAt(now)
                .build();
    }

    /**
     * 将DTO转换为更新实体（保留ID，更新时间戳）
     * @param dto CI任务实例DTO
     * @param existingEntity 现有实体
     * @return 更新后的CI任务实例实体
     */
    public DevOpsCiTaskInstanceEntity toUpdateEntity(DevOpsCiTaskInstance dto, DevOpsCiTaskInstanceEntity existingEntity) {
        if (dto == null || existingEntity == null) {
            return null;
        }

        return DevOpsCiTaskInstanceEntity.builder()
                .id(existingEntity.getId())
                .ciTaskId(existingEntity.getCiTaskId())
                .instanceId(existingEntity.getInstanceId())
                .errorMessage(dto.getErrorMessage() != null ? dto.getErrorMessage() : existingEntity.getErrorMessage())
                .userId(existingEntity.getUserId())
                .createdAt(existingEntity.getCreatedAt())
                .updatedAt(LocalDateTime.now())
                .build();
    }


}
