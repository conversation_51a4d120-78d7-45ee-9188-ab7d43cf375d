package com.example.springvueapp.devops.service;

import com.example.springvueapp.devops.entity.DevOpsCiTaskEntity;
import com.example.springvueapp.devops.entity.DevOpsCiTaskInstanceEntity;
import com.example.springvueapp.devops.entity.DevOpsComponentEntity;
import com.example.springvueapp.devops.mapper.DevOpsCiTaskInstanceMapper;
import com.example.springvueapp.devops.mapper.DevOpsCiTaskMapper;
import com.example.springvueapp.devops.model.DevOpsCiTask;
import com.example.springvueapp.devops.model.DevOpsCiTaskInstance;
import com.example.springvueapp.devops.repository.DevOpsCiTaskInstanceRepository;
import com.example.springvueapp.devops.repository.DevOpsCiTaskRepository;
import com.example.springvueapp.devops.repository.DevOpsComponentRepository;
import io.fabric8.tekton.client.TektonClient;
import io.fabric8.tekton.client.dsl.V1beta1APIGroupDSL;
import io.fabric8.kubernetes.client.dsl.MixedOperation;
import io.fabric8.kubernetes.client.dsl.Resource;
import io.fabric8.tekton.pipeline.v1beta1.TaskRun;
import io.fabric8.tekton.pipeline.v1beta1.TaskRunList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TektonCiTaskService的单元测试
 */
@ExtendWith(MockitoExtension.class)
class TektonCiTaskServiceTest {

    @Mock
    private DevOpsCiTaskRepository ciTaskRepository;

    @Mock
    private DevOpsCiTaskInstanceRepository ciTaskInstanceRepository;

    @Mock
    private DevOpsComponentRepository componentRepository;

    @Mock
    private DevOpsCiTaskMapper ciTaskMapper;

    @Mock
    private DevOpsCiTaskInstanceMapper ciTaskInstanceMapper;

    @Mock
    private TektonClient tektonClient;
    
    @Mock
    private V1beta1APIGroupDSL v1beta1APIGroup;
    
    @Mock
    private MixedOperation<TaskRun, TaskRunList, Resource<TaskRun>> taskRunsOperation;

    @Mock
    private TektonExecutorService tektonExecutorService;

    private TektonCiTaskService tektonCiTaskService;

    @BeforeEach
    void setUp() {
        tektonCiTaskService = new TektonCiTaskService(
                ciTaskRepository,
                ciTaskInstanceRepository,
                componentRepository,
                ciTaskMapper,
                ciTaskInstanceMapper,
                tektonExecutorService
        );
    }

    @Test
    void testCreateCiTask_WithValidInput_ShouldCreateTask() {
        // Given
        Long componentId = 1L;
        Long userId = 10L;
        
        DevOpsComponentEntity component = DevOpsComponentEntity.builder()
                .id(componentId)
                .name("test-component")
                .userId(userId)
                .build();

        Map<String, Object> config = new HashMap<>();
        config.put("image", "maven:3.8-openjdk-17");

        DevOpsCiTask ciTask = DevOpsCiTask.builder()
                .name("test-task")
                .description("测试任务")
                .taskType("build")
                .configuration(config)
                .build();

        DevOpsCiTaskEntity entity = DevOpsCiTaskEntity.builder()
                .id(1L)
                .name("test-task")
                .componentId(componentId)
                .userId(userId)
                .build();

        DevOpsCiTask expectedDto = DevOpsCiTask.builder()
                .id(1L)
                .name("test-task")
                .componentId(componentId)
                .userId(userId)
                .build();

        when(componentRepository.findByUserIdAndId(userId, componentId))
                .thenReturn(Mono.just(component));
        when(ciTaskRepository.existsByComponentIdAndName(componentId, "test-task"))
                .thenReturn(Mono.just(false));
        when(ciTaskMapper.toNewEntity(ciTask, componentId, userId))
                .thenReturn(entity);
        when(ciTaskRepository.save(entity))
                .thenReturn(Mono.just(entity));
        when(ciTaskMapper.toDto(entity))
                .thenReturn(expectedDto);

        // When & Then
        StepVerifier.create(tektonCiTaskService.createCiTask(ciTask, componentId, userId))
                .expectNext(expectedDto)
                .verifyComplete();

        verify(componentRepository).findByUserIdAndId(userId, componentId);
        verify(ciTaskRepository).existsByComponentIdAndName(componentId, "test-task");
        verify(ciTaskRepository).save(entity);
    }

    @Test
    void testCreateCiTask_WithNonExistentComponent_ShouldThrowException() {
        // Given
        Long componentId = 1L;
        Long userId = 10L;
        DevOpsCiTask ciTask = DevOpsCiTask.builder()
                .name("test-task")
                .build();

        when(componentRepository.findByUserIdAndId(userId, componentId))
                .thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(tektonCiTaskService.createCiTask(ciTask, componentId, userId))
                .expectErrorMatches(throwable -> 
                    throwable instanceof IllegalArgumentException &&
                    throwable.getMessage().equals("组件不存在或无权限访问"))
                .verify();

        verify(componentRepository).findByUserIdAndId(userId, componentId);
        verifyNoInteractions(ciTaskRepository);
    }

    @Test
    void testCreateCiTask_WithDuplicateName_ShouldThrowException() {
        // Given
        Long componentId = 1L;
        Long userId = 10L;
        
        DevOpsComponentEntity component = DevOpsComponentEntity.builder()
                .id(componentId)
                .name("test-component")
                .userId(userId)
                .build();

        DevOpsCiTask ciTask = DevOpsCiTask.builder()
                .name("existing-task")
                .build();

        when(componentRepository.findByUserIdAndId(userId, componentId))
                .thenReturn(Mono.just(component));
        when(ciTaskRepository.existsByComponentIdAndName(componentId, "existing-task"))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(tektonCiTaskService.createCiTask(ciTask, componentId, userId))
                .expectErrorMatches(throwable -> 
                    throwable instanceof IllegalArgumentException &&
                    throwable.getMessage().equals("CI任务名称在该组件中已存在"))
                .verify();

        verify(componentRepository).findByUserIdAndId(userId, componentId);
        verify(ciTaskRepository).existsByComponentIdAndName(componentId, "existing-task");
        verify(ciTaskRepository, never()).save(any());
    }

    @Test
    void testGetCiTaskById_WithValidId_ShouldReturnTask() {
        // Given
        Long taskId = 1L;
        Long userId = 10L;

        DevOpsCiTaskEntity entity = DevOpsCiTaskEntity.builder()
                .id(taskId)
                .name("test-task")
                .userId(userId)
                .build();

        DevOpsCiTask expectedDto = DevOpsCiTask.builder()
                .id(taskId)
                .name("test-task")
                .userId(userId)
                .build();

        when(ciTaskRepository.findByUserIdAndId(userId, taskId))
                .thenReturn(Mono.just(entity));
        when(ciTaskMapper.toDto(entity))
                .thenReturn(expectedDto);

        // When & Then
        StepVerifier.create(tektonCiTaskService.getCiTaskById(taskId, userId))
                .expectNext(expectedDto)
                .verifyComplete();

        verify(ciTaskRepository).findByUserIdAndId(userId, taskId);
        verify(ciTaskMapper).toDto(entity);
    }

    @Test
    void testGetCiTaskById_WithNonExistentId_ShouldThrowException() {
        // Given
        Long taskId = 999L;
        Long userId = 10L;

        when(ciTaskRepository.findByUserIdAndId(userId, taskId))
                .thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(tektonCiTaskService.getCiTaskById(taskId, userId))
                .expectErrorMatches(throwable -> 
                    throwable instanceof IllegalArgumentException &&
                    throwable.getMessage().equals("CI任务不存在或无权限访问"))
                .verify();

        verify(ciTaskRepository).findByUserIdAndId(userId, taskId);
        verifyNoInteractions(ciTaskMapper);
    }

    @Test
    void testStartCiTask_WithValidTask_ShouldCreateInstance() {
        // Given
        Long taskId = 1L;
        Long userId = 10L;
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("branch", "main");

        DevOpsCiTaskEntity taskEntity = DevOpsCiTaskEntity.builder()
                .id(taskId)
                .name("test-task")
                .userId(userId)
                .build();

        DevOpsCiTaskInstanceEntity instanceEntity = DevOpsCiTaskInstanceEntity.builder()
                .id(1L)
                .ciTaskId(taskId)
                .instanceId("test-task-12345678")
                .userId(userId)
                .build();

        DevOpsCiTaskInstance expectedInstance = DevOpsCiTaskInstance.builder()
                .id(1L)
                .ciTaskId(taskId)
                .instanceId("test-task-12345678")
                .status("RUNNING")
                .userId(userId)
                .build();

        when(ciTaskRepository.findByUserIdAndId(userId, taskId))
                .thenReturn(Mono.just(taskEntity));
        when(ciTaskInstanceMapper.toNewEntity(any(DevOpsCiTaskInstance.class), eq(taskId), eq(userId)))
                .thenReturn(instanceEntity);
        when(ciTaskInstanceRepository.save(instanceEntity))
                .thenReturn(Mono.just(instanceEntity));
        when(ciTaskInstanceMapper.toDto(instanceEntity))
                .thenReturn(expectedInstance);
        when(ciTaskMapper.toDto(taskEntity))
                .thenReturn(DevOpsCiTask.builder().id(taskId).name("test-task").build());
        when(tektonExecutorService.createCiTaskInstance(any(DevOpsCiTask.class), any(Map.class)))
                .thenReturn(Mono.just(new HashMap<String, Object>()));

        // When & Then
        StepVerifier.create(tektonCiTaskService.startCiTask(taskId, userId, parameters))
                .expectNext(expectedInstance)
                .verifyComplete();

        verify(ciTaskRepository).findByUserIdAndId(userId, taskId);
        verify(ciTaskInstanceRepository).save(instanceEntity);
    }

    @Test
    void testGetAllCiTasks_ShouldReturnAllUserTasks() {
        // Given
        Long userId = 10L;

        DevOpsCiTaskEntity entity1 = DevOpsCiTaskEntity.builder()
                .id(1L)
                .name("task1")
                .userId(userId)
                .build();

        DevOpsCiTaskEntity entity2 = DevOpsCiTaskEntity.builder()
                .id(2L)
                .name("task2")
                .userId(userId)
                .build();

        DevOpsCiTask dto1 = DevOpsCiTask.builder()
                .id(1L)
                .name("task1")
                .userId(userId)
                .build();

        DevOpsCiTask dto2 = DevOpsCiTask.builder()
                .id(2L)
                .name("task2")
                .userId(userId)
                .build();

        when(ciTaskRepository.findByUserId(userId))
                .thenReturn(Flux.just(entity1, entity2));
        when(ciTaskMapper.toDto(entity1))
                .thenReturn(dto1);
        when(ciTaskMapper.toDto(entity2))
                .thenReturn(dto2);

        // When & Then
        StepVerifier.create(tektonCiTaskService.getAllCiTasks(userId))
                .expectNext(dto1)
                .expectNext(dto2)
                .verifyComplete();

        verify(ciTaskRepository).findByUserId(userId);
        verify(ciTaskMapper, times(2)).toDto(any());
    }

    @Test
    void testDeleteCiTask_WithValidId_ShouldDeleteTask() {
        // Given
        Long taskId = 1L;
        Long userId = 10L;

        DevOpsCiTaskEntity entity = DevOpsCiTaskEntity.builder()
                .id(taskId)
                .name("test-task")
                .userId(userId)
                .build();

        when(ciTaskRepository.findByUserIdAndId(userId, taskId))
                .thenReturn(Mono.just(entity));
        when(ciTaskRepository.deleteByUserIdAndId(userId, taskId))
                .thenReturn(Mono.just(1));

        // When & Then
        StepVerifier.create(tektonCiTaskService.deleteCiTask(taskId, userId))
                .expectNext(true)
                .verifyComplete();

        verify(ciTaskRepository).findByUserIdAndId(userId, taskId);
        verify(ciTaskRepository).deleteByUserIdAndId(userId, taskId);
    }

    @Test
    void testGetTaskTemplate_WithBuildType_ShouldReturnBuildTemplate() {
        // When & Then
        StepVerifier.create(tektonCiTaskService.getTaskTemplate("build"))
                .expectNextMatches(template -> {
                    return template.containsKey("apiVersion") &&
                           template.containsKey("kind") &&
                           "tekton.dev/v1beta1".equals(template.get("apiVersion")) &&
                           "Task".equals(template.get("kind"));
                })
                .verifyComplete();
    }

    @Test
    void testGetSupportedTaskTypes_ShouldReturnAllTypes() {
        // When & Then
        StepVerifier.create(tektonCiTaskService.getSupportedTaskTypes())
                .expectNext("build")
                .expectNext("test")
                .expectNext("deploy")
                .expectNext("custom")
                .verifyComplete();
    }

    @Test
    void testCheckConnection_ShouldReturnTrue() {
        // When & Then
        StepVerifier.create(tektonCiTaskService.checkConnection())
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testGetPlatformInfo_ShouldReturnPlatformDetails() {
        // When & Then
        StepVerifier.create(tektonCiTaskService.getPlatformInfo())
                .expectNextMatches(info -> {
                    return "Tekton".equals(info.get("platform")) &&
                           "v0.50.0".equals(info.get("version")) &&
                           "connected".equals(info.get("status"));
                })
                .verifyComplete();
    }

    @Test
    void testValidateConfiguration_WithValidConfig_ShouldReturnTrue() {
        // Given
        Map<String, Object> config = new HashMap<>();
        config.put("image", "maven:3.8-openjdk-17");
        config.put("script", "mvn clean compile");

        // When & Then
        StepVerifier.create(tektonCiTaskService.validateConfiguration(config))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testValidateConfiguration_WithNullConfig_ShouldReturnTrue() {
        // When & Then
        StepVerifier.create(tektonCiTaskService.validateConfiguration(null))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testTektonExecutorService_Integration_ShouldWork() {
        // Given
        Long taskId = 1L;

        DevOpsCiTask ciTask = DevOpsCiTask.builder()
                .id(taskId)
                .name("test-task")
                .taskType("build")
                .build();

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("branch", "main");

        // Mock TektonExecutorService behavior
        when(tektonExecutorService.createCiTaskInstance(ciTask, parameters))
                .thenReturn(Mono.just(new HashMap<String, Object>()));

        // When & Then
        StepVerifier.create(tektonExecutorService.createCiTaskInstance(ciTask, parameters))
                .expectNext(new HashMap<String, Object>())
                .verifyComplete();

        verify(tektonExecutorService).createCiTaskInstance(ciTask, parameters);
    }

    @Test
    void testTektonExecutorService_WithException_ShouldHandleError() {
        // Given
        Long taskId = 1L;
        DevOpsCiTask ciTask = DevOpsCiTask.builder()
                .id(taskId)
                .name("test-task")
                .taskType("build")
                .build();

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("branch", "main");

        // Mock TektonExecutorService to throw exception
        when(tektonExecutorService.createCiTaskInstance(ciTask, parameters))
                .thenReturn(Mono.error(new RuntimeException("Tekton API error")));

        // When & Then
        StepVerifier.create(tektonExecutorService.createCiTaskInstance(ciTask, parameters))
                .expectError(RuntimeException.class)
                .verify();

        verify(tektonExecutorService).createCiTaskInstance(ciTask, parameters);
    }
}
