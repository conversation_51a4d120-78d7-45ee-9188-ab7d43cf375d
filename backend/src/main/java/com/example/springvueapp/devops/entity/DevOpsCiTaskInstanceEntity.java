package com.example.springvueapp.devops.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * DevOps CI任务实例的数据库实体类
 * 对应devops_ci_task_instances表
 */
@Table("devops_ci_task_instances")
public class DevOpsCiTaskInstanceEntity {

    @Id
    private Long id;

    private Long ciTaskId;

    private String instanceId;

    private String errorMessage; // 保留错误信息用于调试

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsCiTaskInstanceEntity() {
    }

    public DevOpsCiTaskInstanceEntity(Long id, Long ciTaskId, String instanceId,
                                     String errorMessage, Long userId,
                                     LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.ciTaskId = ciTaskId;
        this.instanceId = instanceId;
        this.errorMessage = errorMessage;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCiTaskId() {
        return ciTaskId;
    }

    public void setCiTaskId(Long ciTaskId) {
        this.ciTaskId = ciTaskId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }



    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsCiTaskInstanceEntity{" +
                "id=" + id +
                ", ciTaskId=" + ciTaskId +
                ", instanceId='" + instanceId + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * DevOpsCiTaskInstanceEntity 的构建器类
     */
    public static class Builder {
        private DevOpsCiTaskInstanceEntity instance = new DevOpsCiTaskInstanceEntity();

        public Builder id(Long id) {
            instance.setId(id);
            return this;
        }

        public Builder ciTaskId(Long ciTaskId) {
            instance.setCiTaskId(ciTaskId);
            return this;
        }

        public Builder instanceId(String instanceId) {
            instance.setInstanceId(instanceId);
            return this;
        }



        public Builder errorMessage(String errorMessage) {
            instance.setErrorMessage(errorMessage);
            return this;
        }

        public Builder userId(Long userId) {
            instance.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            instance.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            instance.setUpdatedAt(updatedAt);
            return this;
        }

        public DevOpsCiTaskInstanceEntity build() {
            return instance;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
